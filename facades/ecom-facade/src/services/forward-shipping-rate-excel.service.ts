import {injectable} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import * as XLSX from 'xlsx';

export interface ExcelShippingRateRow {
  weightSlab: string;
  localRate: number;
  zonalRate: number;
  nationalRate: number;
}

@injectable()
export class ForwardShippingRateExcelService {
  constructor() {}

  /**
   * Parse Excel file and extract shipping rate data
   * @param fileBuffer - Buffer containing the Excel file data
   * @returns Array of shipping rate objects
   */
  parseExcelFile(fileBuffer: Buffer): ExcelShippingRateRow[] {
    try {
      // Read the workbook from buffer
      const workbook = XLSX.read(fileBuffer, {type: 'buffer'});

      // Get the first worksheet
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new HttpErrors.BadRequest('Excel file contains no worksheets');
      }

      const worksheet = workbook.Sheets[sheetName];

      // Convert worksheet to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as unknown[][];

      if (jsonData.length < 2) {
        throw new HttpErrors.BadRequest(
          'Excel file must contain at least a header row and one data row',
        );
      }

      // Validate and parse the data
      return this.validateAndParseData(jsonData);
    } catch (error) {
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      throw new HttpErrors.BadRequest(
        `Failed to parse Excel file: ${error.message}`,
      );
    }
  }

  /**
   * Validate Excel data format and parse into shipping rate objects
   * @param data - Raw Excel data as 2D array
   * @returns Array of validated shipping rate objects
   */
  private validateAndParseData(data: unknown[][]): ExcelShippingRateRow[] {
    const headerRow = data[0];

    // Expected headers (case-insensitive)
    const expectedHeaders = ['WEIGHT SLAB', 'Local', 'Zonal', 'National'];

    // Validate headers
    if (!this.validateHeaders(headerRow, expectedHeaders)) {
      throw new HttpErrors.BadRequest(
        `Invalid Excel format. Expected headers: ${expectedHeaders.join(', ')}`,
      );
    }

    const shippingRates: ExcelShippingRateRow[] = [];

    // Process data rows (skip header)
    for (let i = 1; i < data.length; i++) {
      const row = data[i];

      // Skip empty rows
      if (!row || row.every(cell => !cell)) {
        continue;
      }

      try {
        const shippingRate = this.parseDataRow(row, i + 1);
        shippingRates.push(shippingRate);
      } catch (error) {
        throw new HttpErrors.BadRequest(
          `Error in row ${i + 1}: ${error.message}`,
        );
      }
    }

    if (shippingRates.length === 0) {
      throw new HttpErrors.BadRequest('No valid data rows found in Excel file');
    }

    return shippingRates;
  }

  /**
   * Validate Excel headers
   * @param headers - Header row from Excel
   * @param expectedHeaders - Expected header names
   * @returns true if headers are valid
   */
  private validateHeaders(
    headers: unknown[],
    expectedHeaders: string[],
  ): boolean {
    if (!headers || headers.length < expectedHeaders.length) {
      return false;
    }

    for (let i = 0; i < expectedHeaders.length; i++) {
      const header = headers[i]?.toString().trim();
      const expected = expectedHeaders[i];

      if (!header || header.toLowerCase() !== expected.toLowerCase()) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validate file extension
   * @param filename - Name of the uploaded file
   * @returns true if file extension is valid
   */
  validateFileExtension(filename: string): boolean {
    if (!filename) {
      return false;
    }
    return /\.(xlsx|xls)$/i.test(filename);
  }

  /**
   * Validate file size
   * @param fileSize - Size of the file in bytes
   * @param maxSizeInMB - Maximum allowed size in MB (default: 5MB)
   * @returns true if file size is valid
   */
  validateFileSize(fileSize: number, maxSizeInMB: number = 5): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return fileSize <= maxSizeInBytes;
  }

  /**
   * Parse a single data row from Excel
   * @param row - Data row array
   * @param rowNumber - Row number for error reporting
   * @returns Parsed shipping rate object
   */
  private parseDataRow(
    row: unknown[],
    rowNumber: number,
  ): ExcelShippingRateRow {
    if (!row || row.length < 4) {
      throw new Error(`Row must contain at least 4 columns`);
    }

    const weightSlab = row[0]?.toString().trim();
    if (!weightSlab) {
      throw new Error('Weight slab cannot be empty');
    }

    const localRate = this.parseRate(row[1], 'Local rate');
    const zonalRate = this.parseRate(row[2], 'Zonal rate');
    const nationalRate = this.parseRate(row[3], 'National rate');

    return {
      weightSlab,
      localRate,
      zonalRate,
      nationalRate,
    };
  }

  /**
   * Parse and validate a rate value
   * @param value - Raw value from Excel
   * @param fieldName - Field name for error reporting
   * @returns Parsed numeric rate
   */
  private parseRate(value: unknown, fieldName: string): number {
    if (value === null || value === undefined || value === '') {
      throw new Error(`${fieldName} cannot be empty`);
    }

    // Handle currency symbols and formatting
    let cleanValue = value.toString().trim();

    // Remove currency symbols (₹, $, etc.) and commas
    cleanValue = cleanValue.replace(/[₹$,\s]/g, '');

    const numericValue = parseFloat(cleanValue);

    if (isNaN(numericValue) || numericValue < 0) {
      throw new Error(`${fieldName} must be a valid positive number`);
    }

    return numericValue;
  }

  /**
   * Convert parsed Excel data to ForwardShippingRate model objects
   * @param excelData - Parsed Excel data
   * @returns Array of ForwardShippingRate objects ready for database insertion
   */
  convertToModelObjects(excelData: ExcelShippingRateRow[]): {
    weightSlab: string;
    localRate: number;
    zonalRate: number;
    nationalRate: number;
  }[] {
    return excelData.map(row => ({
      weightSlab: row.weightSlab,
      localRate: row.localRate,
      zonalRate: row.zonalRate,
      nationalRate: row.nationalRate,
    }));
  }
}
