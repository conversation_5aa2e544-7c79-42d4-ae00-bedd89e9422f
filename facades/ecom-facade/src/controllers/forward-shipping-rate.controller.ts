import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  Request,
  RestBindings,
  HttpErrors,
} from '@loopback/rest';
import {inject, service} from '@loopback/core';
import {ForwardShippingRate} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {ForwardShippingRateExcelService} from '../services';

const basePath = '/forward-shipping-rates';

interface RequestWithFiles extends Request {
  files?: Express.Multer.File[];
}

export class ForwardShippingRateController {
  constructor(
    @restService(ForwardShippingRate)
    private readonly forwardShippingRateProxy: ModifiedRestService<ForwardShippingRate>,
    @service(ForwardShippingRateExcelService)
    private readonly excelService: ForwardShippingRateExcelService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateShippingRule]})
  @post(`${basePath}/upload-excel`)
  @response(STATUS_CODE.OK, {
    description: 'Forward shipping rates uploaded from Excel file',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
            count: {type: 'number'},
          },
        },
      },
    },
  })
  async uploadExcel(): Promise<{message: string; count: number}> {
    // Check if file is uploaded
    const files = (this.request as RequestWithFiles).files;
    if (!files?.length) {
      throw new HttpErrors.BadRequest('No Excel file uploaded');
    }

    const file = files[0];

    // Validate file extension
    if (!this.excelService.validateFileExtension(file.originalname)) {
      throw new HttpErrors.BadRequest(
        'Unsupported file format. Only Excel files (.xlsx, .xls) are supported',
      );
    }

    // Validate file size (5MB limit)
    if (!this.excelService.validateFileSize(file.size)) {
      throw new HttpErrors.BadRequest(
        'File size too large. Maximum allowed size is 5MB',
      );
    }

    // Validate file buffer
    if (!file.buffer || file.buffer.length === 0) {
      throw new HttpErrors.BadRequest('Uploaded file is empty or corrupted');
    }

    try {
      // Parse Excel file
      const excelData = this.excelService.parseExcelFile(file.buffer);

      // Convert to model objects
      const shippingRates = this.excelService.convertToModelObjects(excelData);

      // Clear existing data
      const existingRates = await this.forwardShippingRateProxy.find();
      await Promise.all(
        existingRates.map(rate =>
          rate.id
            ? this.forwardShippingRateProxy.deleteById(rate.id)
            : Promise.resolve(),
        ),
      );

      // Insert new data
      const createdRates = await Promise.all(
        shippingRates.map(rate => this.forwardShippingRateProxy.create(rate)),
      );

      return {
        message: 'Forward shipping rates uploaded successfully',
        count: createdRates.length,
      };
    } catch (error) {
      // Re-throw HTTP errors (validation errors, etc.)
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }

      // Log the error for debugging
      console.error('Excel processing error:', error);

      // Handle specific error types
      if (error.message?.includes('Invalid file format')) {
        throw new HttpErrors.BadRequest('Invalid Excel file format');
      }

      if (
        error.message?.includes('ENOENT') ||
        error.message?.includes('file not found')
      ) {
        throw new HttpErrors.BadRequest('File could not be read');
      }

      // Generic error for unexpected issues
      throw new HttpErrors.InternalServerError(
        'An error occurred while processing the Excel file. Please check the file format and try again.',
      );
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateShippingRule]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ForwardShippingRate model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ForwardShippingRate)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ForwardShippingRate, {
            title: 'NewForwardShippingRate',
            exclude: ['id'],
          }),
        },
      },
    })
    forwardShippingRate: Omit<ForwardShippingRate, 'id'>,
  ): Promise<ForwardShippingRate> {
    return this.forwardShippingRateProxy.create(forwardShippingRate);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewShippingRule]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ForwardShippingRate model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(ForwardShippingRate) where?: Where<ForwardShippingRate>,
  ): Promise<Count> {
    return this.forwardShippingRateProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewShippingRule]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ForwardShippingRate model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ForwardShippingRate, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(ForwardShippingRate) filter?: Filter<ForwardShippingRate>,
  ): Promise<ForwardShippingRate[]> {
    return this.forwardShippingRateProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewShippingRule]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ForwardShippingRate model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ForwardShippingRate, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ForwardShippingRate, {exclude: 'where'})
    filter?: FilterExcludingWhere<ForwardShippingRate>,
  ): Promise<ForwardShippingRate> {
    return this.forwardShippingRateProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateShippingRule]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ForwardShippingRate PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ForwardShippingRate, {partial: true}),
        },
      },
    })
    forwardShippingRate: ForwardShippingRate,
  ): Promise<void> {
    await this.forwardShippingRateProxy.updateById(id, forwardShippingRate);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateShippingRule]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ForwardShippingRate PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() forwardShippingRate: ForwardShippingRate,
  ): Promise<void> {
    await this.forwardShippingRateProxy.replaceById(id, forwardShippingRate);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteShippingRule]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ForwardShippingRate DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.forwardShippingRateProxy.deleteById(id);
  }
}
