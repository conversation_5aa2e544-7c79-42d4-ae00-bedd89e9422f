{"name": "ec-facade-ecom-facade", "version": "0.0.1", "description": "ecom-facade", "keywords": ["loopback-microservice", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18"}, "scripts": {"build": "lb-tsc && cp -r src/templates dist/templates", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js", "docker:build": "DOCKER_BUILDKIT=1 sudo docker build --build-arg NR_ENABLED=$NR_ENABLED_VALUE --build-arg FROM_FOLDER=services --build-arg SERVICE_NAME=undefined -t $IMAGE_REPO_NAME/$npm_package_name:$npm_package_version ../../. -f ./Dockerfile", "docker:run": "docker run -p 3000:3000 -d ecom-facade", "symlink-resolver": "symlink-resolver", "resolve-links": "npm run symlink-resolver build ./node_modules/@local", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run clean && npm run openapi-spec", "start": "node -r ./dist/opentelemetry-registry.js -r source-map-support/register .", "dev": "nodemon --watch src -e ts --exec \"npm run start\"", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build", "docker:push": " docker push $IMAGE_REPO_NAME/$npm_package_name:$npm_package_version", "docker:build:dev": "DOCKER_BUILDKIT=1 sudo docker build --build-arg NR_ENABLED=$NR_ENABLED_VALUE --build-arg FROM_FOLDER=services --build-arg SERVICE_NAME=undefined -t $IMAGE_REPO_NAME/$npm_package_name:$npm_package_version ../../. -f ./Dockerfile", "docker:push:dev": " docker push $IMAGE_REPO_NAME/$npm_package_name:$npm_package_version", "coverage": "nyc npm run test"}, "repository": {"type": "git", "url": ""}, "author": "rithikBnb<<EMAIL>>", "license": "MIT", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@loopback/boot": "^7.0.9", "@loopback/context": "^7.0.9", "@loopback/core": "^6.1.6", "@loopback/openapi-v3": "^10.0.9", "@loopback/repository": "^7.0.14", "@loopback/rest": "^14.0.9", "@loopback/rest-explorer": "^7.0.9", "@loopback/service-proxy": "^7.0.12", "@opentelemetry/exporter-jaeger": "^1.15.0", "@opentelemetry/plugin-dns": "^0.15.0", "@opentelemetry/plugin-http": "^0.18.2", "@opentelemetry/plugin-https": "^0.18.2", "@opentelemetry/plugin-pg": "^0.15.0", "@opentelemetry/plugin-pg-pool": "^0.15.0", "@opentelemetry/sdk-trace-base": "^1.15.0", "@opentelemetry/sdk-trace-node": "^1.15.0", "@sourceloop/authentication-service": "^20.0.3", "@sourceloop/core": "^15.0.2", "@sourceloop/file-utils": "^0.2.1", "@types/xlsx": "^0.0.35", "cashfree-pg": "^5.0.8", "clamscan": "^2.4.0", "dotenv": "^16.4.5", "dotenv-extended": "^2.9.0", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "loopback-connector-kv-redis": "^4.0.0", "loopback-connector-rest": "^5.0.7", "loopback4-authentication": "^12.1.1", "loopback4-authorization": "^7.0.3", "loopback4-helmet": "^7.0.3", "loopback4-notifications": "^9.0.1", "loopback4-ratelimiter": "^7.0.6", "mime-types": "^3.0.1", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "path": "^0.12.7", "swagger-stats": "^0.99.5", "symlink-resolver": "0.2.1", "to-words": "^4.6.0", "tslib": "^2.6.2", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@loopback/build": "^11.0.8", "@loopback/eslint-config": "^15.0.4", "@loopback/testlab": "^7.0.8", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/node": "^20.12.7", "@types/uuid": "^10.0.0", "eslint": "^8.57.0", "nodemon": "^2.0.21", "nyc": "^15.1.0", "source-map-support": "^0.5.21", "typescript": "^5.4.5"}}